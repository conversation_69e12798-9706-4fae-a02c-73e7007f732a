#!/usr/bin/env python3
"""
Test script to verify the complete workflow from Data Sources → Airtable → Dashboard Generation
"""

import json
import os
import sys
from pathlib import Path

def test_complete_workflow():
    """Test the complete workflow and verify configuration is applied correctly"""
    
    print("🧪 Testing Complete Workflow")
    print("=" * 60)
    
    # Expected values for Ready Set Repair
    expected_base_id = "appJWDQxzECcXvWz7"
    expected_ghl_table_id = "tblgNNUwWlZ8iWZ0P"
    expected_google_ads_table_id = "tblOg7U7VYjWTpYeu"
    
    # Test paths
    test_output_dir = "Ready Set Repair/Ready Set Repair"
    client_config_path = os.path.join(test_output_dir, "client_config.json")
    config_py_path = os.path.join(test_output_dir, "config.py")
    generated_config_path = os.path.join(test_output_dir, "Ready Set Repair_config.json")
    
    print("📋 Step 1: Check Generated Airtable Configuration")
    print("-" * 40)
    
    if os.path.exists(generated_config_path):
        with open(generated_config_path, 'r') as f:
            generated_config = json.load(f)
        
        airtable_config = generated_config.get("airtable_configuration", {})
        base_id = airtable_config.get("base_id")
        ghl_table_id = airtable_config.get("ghl_table_id")
        google_ads_table_id = airtable_config.get("google_ads_table_id")
        
        print(f"✅ Generated config found")
        print(f"   Base ID: {base_id}")
        print(f"   GHL Table ID: {ghl_table_id}")
        print(f"   Google Ads Table ID: {google_ads_table_id}")
        
        # Verify generated config has correct values
        if base_id == expected_base_id:
            print(f"✅ Generated base ID is correct")
        else:
            print(f"❌ Generated base ID is wrong: {base_id}")
            
    else:
        print(f"❌ Generated config not found: {generated_config_path}")
        return
    
    print()
    print("📋 Step 2: Check Applied Configuration in Web App")
    print("-" * 40)
    
    # Test client_config.json
    if os.path.exists(client_config_path):
        with open(client_config_path, 'r') as f:
            client_config = json.load(f)
        
        # Check base ID
        actual_base_id = client_config.get("airtable_configuration", {}).get("base_id")
        print(f"📄 client_config.json:")
        print(f"   Base ID: {actual_base_id}")
        
        if actual_base_id == expected_base_id:
            print(f"   ✅ Base ID correctly applied")
        else:
            print(f"   ❌ Base ID NOT applied (expected: {expected_base_id})")
        
        # Check enabled sources
        enabled_sources = client_config.get("data_sources", {}).get("enabled_sources", [])
        print(f"   Enabled sources: {enabled_sources}")
        
        # Check table IDs
        source_configs = client_config.get("data_sources", {}).get("source_configs", {})
        
        if "ghl" in source_configs:
            ghl_table_id = source_configs["ghl"].get("table_id")
            print(f"   GHL table ID: {ghl_table_id}")
            if ghl_table_id == expected_ghl_table_id:
                print(f"   ✅ GHL table ID correctly applied")
            else:
                print(f"   ❌ GHL table ID NOT applied (expected: {expected_ghl_table_id})")
        
        if "google_ads" in source_configs:
            gads_table_id = source_configs["google_ads"].get("table_id")
            print(f"   Google Ads table ID: {gads_table_id}")
            if gads_table_id == expected_google_ads_table_id:
                print(f"   ✅ Google Ads table ID correctly applied")
            else:
                print(f"   ❌ Google Ads table ID NOT applied (expected: {expected_google_ads_table_id})")
    
    else:
        print(f"❌ client_config.json not found: {client_config_path}")
    
    print()
    
    # Test config.py
    if os.path.exists(config_py_path):
        with open(config_py_path, 'r') as f:
            config_content = f.read()
        
        print(f"🐍 config.py:")
        
        # Check base ID in config.py
        if expected_base_id in config_content:
            print(f"   ✅ Base ID found: {expected_base_id}")
        else:
            print(f"   ❌ Base ID NOT found: {expected_base_id}")
        
        # Check table IDs in config.py
        if expected_ghl_table_id in config_content:
            print(f"   ✅ GHL table ID found: {expected_ghl_table_id}")
        else:
            print(f"   ❌ GHL table ID NOT found: {expected_ghl_table_id}")
        
        if expected_google_ads_table_id in config_content:
            print(f"   ✅ Google Ads table ID found: {expected_google_ads_table_id}")
        else:
            print(f"   ❌ Google Ads table ID NOT found: {expected_google_ads_table_id}")
    
    else:
        print(f"❌ config.py not found: {config_py_path}")
    
    print()
    print("📋 Step 3: Check File Structure")
    print("-" * 40)
    
    if os.path.exists(test_output_dir):
        files = os.listdir(test_output_dir)
        
        # Essential files
        essential_files = [
            'index.html', 'script.js', 'styles.css', 'server.py', 
            'config.py', 'client_config.json', 'requirements.txt'
        ]
        
        print("📄 Essential files:")
        for file in essential_files:
            if file in files:
                print(f"   ✅ {file}")
            else:
                print(f"   ❌ {file} (missing)")
        
        # Check for unwanted files/folders
        unwanted_items = [
            'clients', 'logs', 'Ready Set Repair_backup_', 'DBCust.py', 
            'RLEx.py', 'dashboard_customizer.log', '__pycache__'
        ]
        
        found_unwanted = []
        for item in files:
            for unwanted in unwanted_items:
                if unwanted in item:
                    found_unwanted.append(item)
        
        if found_unwanted:
            print(f"⚠️ Unwanted items found:")
            for item in found_unwanted:
                print(f"   🚫 {item}")
        else:
            print("✅ No unwanted files/folders found")
    
    else:
        print(f"❌ Output directory not found: {test_output_dir}")
    
    print("=" * 60)
    print("🧪 Test completed!")

if __name__ == "__main__":
    test_complete_workflow()
