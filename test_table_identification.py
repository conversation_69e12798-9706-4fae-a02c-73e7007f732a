#!/usr/bin/env python3
"""
Test script to verify table identification logic
"""

import sys
import os

# Add the current directory to the path so we can import DBCust
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from DBCust import AttributionDashboardCustomizer
    print("✅ Successfully imported AttributionDashboardCustomizer")
except Exception as e:
    print(f"❌ Import error: {e}")
    sys.exit(1)

def test_table_identification():
    """Test the table identification logic with sample table names"""
    
    # Create an instance of the customizer
    customizer = AttributionDashboardCustomizer()
    
    # Test table names from your screenshot
    test_tables = [
        "Google Ads",
        "POS", 
        "Meta Ads",
        "GHL",
        "Ready Set Repair",  # This should not match any type
        "Test Table"         # This should not match any type
    ]
    
    print("🔍 Testing Table Identification Logic")
    print("=" * 50)
    
    for table_name in test_tables:
        table_type = customizer._identify_table_type(table_name)
        status = "✅ IDENTIFIED" if table_type else "❓ UNKNOWN"
        print(f"{status}: '{table_name}' -> {table_type}")
    
    print("\n" + "=" * 50)
    
    # Test the configuration generation with sample base data
    sample_base_data = {
        'id': 'app7ffftdM6e3yekG',
        'name': 'Ready Set Repair',
        'tables': [
            {'id': 'tblGoogleAdsTest123', 'name': 'Google Ads'},
            {'id': 'tblPOSTest456', 'name': 'POS'},
            {'id': 'tblMetaAdsTest789', 'name': 'Meta Ads'},
            {'id': 'tblGHLTest012', 'name': 'GHL'}
        ]
    }
    
    print("🔧 Testing Configuration Generation")
    print("=" * 50)
    
    config_json = customizer._generate_single_client_config(sample_base_data)
    print("Generated Configuration:")
    print(config_json)

if __name__ == "__main__":
    test_table_identification()
