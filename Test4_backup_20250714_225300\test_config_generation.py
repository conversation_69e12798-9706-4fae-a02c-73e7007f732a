#!/usr/bin/env python3
"""
Test script to verify Airtable configuration generation is working correctly
"""

import json
import os
import sys
from pathlib import Path

def test_generated_config():
    """Test that the generated configuration has correct values"""
    
    # Test paths
    test_output_dir = "Ready Set Repair/Ready Set Repair"
    client_config_path = os.path.join(test_output_dir, "client_config.json")
    config_py_path = os.path.join(test_output_dir, "config.py")
    
    print("🧪 Testing Generated Configuration")
    print("=" * 50)
    
    # Expected values for Ready Set Repair
    expected_base_id = "appJWDQxzECcXvWz7"
    expected_ghl_table_id = "tblgNNUwWlZ8iWZ0P"
    expected_google_ads_table_id = "tblOg7U7VYjWTpYeu"
    
    # Test 1: Check client_config.json
    print("📋 Testing client_config.json...")
    
    if os.path.exists(client_config_path):
        with open(client_config_path, 'r') as f:
            client_config = json.load(f)
        
        # Check base ID
        actual_base_id = client_config.get("airtable_configuration", {}).get("base_id")
        if actual_base_id == expected_base_id:
            print(f"✅ Base ID correct: {actual_base_id}")
        else:
            print(f"❌ Base ID incorrect: {actual_base_id} (expected: {expected_base_id})")
        
        # Check enabled sources
        enabled_sources = client_config.get("data_sources", {}).get("enabled_sources", [])
        print(f"📊 Enabled sources: {enabled_sources}")
        
        # Check table IDs
        source_configs = client_config.get("data_sources", {}).get("source_configs", {})
        
        if "ghl" in source_configs:
            ghl_table_id = source_configs["ghl"].get("table_id")
            if ghl_table_id == expected_ghl_table_id:
                print(f"✅ GHL table ID correct: {ghl_table_id}")
            else:
                print(f"❌ GHL table ID incorrect: {ghl_table_id} (expected: {expected_ghl_table_id})")
        
        if "google_ads" in source_configs:
            gads_table_id = source_configs["google_ads"].get("table_id")
            if gads_table_id == expected_google_ads_table_id:
                print(f"✅ Google Ads table ID correct: {gads_table_id}")
            else:
                print(f"❌ Google Ads table ID incorrect: {gads_table_id} (expected: {expected_google_ads_table_id})")
    
    else:
        print(f"❌ client_config.json not found at: {client_config_path}")
    
    print()
    
    # Test 2: Check config.py
    print("🐍 Testing config.py...")
    
    if os.path.exists(config_py_path):
        with open(config_py_path, 'r') as f:
            config_content = f.read()
        
        # Check base ID in config.py
        if expected_base_id in config_content:
            print(f"✅ Base ID found in config.py: {expected_base_id}")
        else:
            print(f"❌ Base ID not found in config.py: {expected_base_id}")
        
        # Check table IDs in config.py
        if expected_ghl_table_id in config_content:
            print(f"✅ GHL table ID found in config.py: {expected_ghl_table_id}")
        else:
            print(f"❌ GHL table ID not found in config.py: {expected_ghl_table_id}")
        
        if expected_google_ads_table_id in config_content:
            print(f"✅ Google Ads table ID found in config.py: {expected_google_ads_table_id}")
        else:
            print(f"❌ Google Ads table ID not found in config.py: {expected_google_ads_table_id}")
    
    else:
        print(f"❌ config.py not found at: {config_py_path}")
    
    print()
    
    # Test 3: Check file structure
    print("📁 Testing file structure...")
    
    if os.path.exists(test_output_dir):
        files = os.listdir(test_output_dir)
        essential_files = [
            'index.html', 'script.js', 'styles.css', 'server.py', 
            'config.py', 'client_config.json', 'requirements.txt'
        ]
        
        missing_files = []
        for file in essential_files:
            if file in files:
                print(f"✅ {file}")
            else:
                missing_files.append(file)
                print(f"❌ {file} (missing)")
        
        # Check for unwanted files/folders
        unwanted_items = [
            'clients', 'logs', 'Ready Set Repair_backup_', 'DBCust.py', 
            'RLEx.py', 'dashboard_customizer.log'
        ]
        
        found_unwanted = []
        for item in files:
            for unwanted in unwanted_items:
                if unwanted in item:
                    found_unwanted.append(item)
        
        if found_unwanted:
            print(f"⚠️ Unwanted items found: {found_unwanted}")
        else:
            print("✅ No unwanted files/folders found")
    
    else:
        print(f"❌ Output directory not found: {test_output_dir}")
    
    print("=" * 50)
    print("🧪 Test completed!")

if __name__ == "__main__":
    test_generated_config()
