#!/usr/bin/env python3
"""
Simple test script for DBCust.py enhancements
Tests basic import and class availability
"""

import sys
import os

def test_basic_import():
    """Test basic import and class availability"""
    print("🧪 Testing DBCust.py Enhanced Import")
    print("=" * 50)

    try:
        # Test basic import
        import DBCust
        print("✅ DBCust module imported successfully")

        # Test class availability
        classes_to_check = [
            'ClientInstanceGenerator',
            'ClientConfig',
            'DataSourceConfig',
            'DashboardCustomizerGUI'
        ]

        for class_name in classes_to_check:
            if hasattr(DBCust, class_name):
                print(f"✅ {class_name} class available")
            else:
                print(f"❌ {class_name} class missing")

        # Test ClientInstanceGenerator methods
        generator = DBCust.ClientInstanceGenerator()
        enhanced_methods = [
            '_validate_airtable_configuration',
            '_generate_local_development_files',
            '_log_configuration_summary'
        ]

        print(f"\n🔍 Checking enhanced methods:")
        for method_name in enhanced_methods:
            if hasattr(generator, method_name):
                print(f"✅ {method_name} method available")
            else:
                print(f"❌ {method_name} method missing")

        print(f"\n🎉 Basic import test completed successfully!")
        print(f"   All enhanced classes and methods are available")
        return True

    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_creation():
    """Test creating a basic configuration"""
    print("\n🧪 Testing Configuration Creation")
    print("=" * 50)

    try:
        from DBCust import ClientConfig, DataSourceConfig

        # Create a test configuration
        config = ClientConfig(
            client_id="test_client",
            business_name="Test Business",
            airtable_base_id="appTestBase123"
        )

        print("✅ ClientConfig created successfully")
        print(f"   • Business: {config.business_name}")
        print(f"   • Base ID: {config.airtable_base_id}")

        # Test data source config
        ds_config = DataSourceConfig(
            table_id="tblTest123",
            enabled=True
        )

        print("✅ DataSourceConfig created successfully")
        print(f"   • Table ID: {ds_config.table_id}")
        print(f"   • Enabled: {ds_config.enabled}")

        return True

    except Exception as e:
        print(f"❌ Configuration test failed: {e}")
        return False

if __name__ == "__main__":
    success1 = test_basic_import()
    success2 = test_config_creation()

    if success1 and success2:
        print(f"\n🎉 All tests passed! DBCust.py enhancements are working correctly.")
    else:
        print(f"\n❌ Some tests failed. Please check the errors above.")
