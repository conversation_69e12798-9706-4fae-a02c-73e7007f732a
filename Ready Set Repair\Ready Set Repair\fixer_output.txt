Traceback (most recent call last):
  File "C:\Users\<USER>\Downloads\RL Tools\fix_airtable_config.py", line 171, in <module>
    main()
  File "C:\Users\<USER>\Downloads\RL Tools\fix_airtable_config.py", line 163, in main
    fixes_applied = fix_airtable_configuration(dashboard_dir, base_id, table_mappings)
                    ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Users\<USER>\Downloads\RL Tools\fix_airtable_config.py", line 22, in fix_airtable_configuration
    print(f"\U0001f527 Fixing Airtable configuration in: {dashboard_dir}")
  File "C:\Users\<USER>\AppData\Local\Programs\Python\Python312\Lib\encodings\cp1252.py", line 19, in encode
    return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
UnicodeEncodeError: 'charmap' codec can't encode character '\U0001f527' in position 0: character maps to <undefined>
