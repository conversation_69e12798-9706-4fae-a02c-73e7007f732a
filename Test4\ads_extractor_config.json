{"credentials": {"customer_id": "************", "developer_token": "bGBR3u83ozZjmx1WXckSkQ", "refresh_token": "1//01NsefTx-HCBvCgYIARAAGAESNwF-L9IrG7rF3r0GzraiGQPO-cBWzppPxA0pggmi8KtWhZqpSZBDUAG3J_qbKx1xWo06hY9GaSc", "manager_account_id": "**********"}, "clients": [{"customer_id": "**********", "customer_id_formatted": "613-849-0164", "client_name": "QuickFix", "notes": "Ad Spend Data", "added_date": "2025-05-23T16:07:02.914848"}, {"client_name": "TestClient_AutoDetect", "customer_id": "", "customer_id_formatted": "", "notes": "Auto-detected from folder: TestClient_AutoDetect", "added_date": "2025-07-03T15:19:39.083139", "auto_detected": true}, {"client_name": "Fix My Gadget", "customer_id": "**********", "customer_id_formatted": "************", "notes": "", "added_date": "2025-07-03T17:33:34.486659", "auto_detected": false, "table_ids": {"google_ads": "tblFixMyGadgeGAds", "ghl": "tblFixMyGadgeGHL"}}, {"client_name": "iGenius Phone Repair", "customer_id": "**********", "customer_id_formatted": "************", "notes": "", "added_date": "2025-07-04T14:10:34.782112", "auto_detected": false, "table_ids": {"google_ads": "tblIGeniusPhoGAds", "ghl": "tblIGeniusPhoGHL"}}, {"client_name": "Test Client LLC", "customer_id": "", "customer_id_formatted": "", "notes": "Auto-detected from folder: Test Client LLC", "added_date": "2025-07-10T15:55:58.002496", "auto_detected": true}], "saved_sheets": [{"id": "1oN6s0XtE3fj3lzpUNBocj0CQMkBYyJXmoEtbuSsJd3o", "name": "QuickFix", "description": "Created on 2025-05-23", "added_date": "2025-05-23T16:11:06.125364"}], "last_date_preset": "Custom", "last_export_format": "csv", "scheduled_tasks": [], "window_geometry": "1664x861+128+110", "theme": "lumen", "auto_save": true, "show_tooltips": true, "airtable": {"api_key": "**********************************************************************************", "base_id": "app7ffftdM6e3yekG", "google_ads_table_id": "tblRBXdh6L6zm9CZn", "auto_sync": false, "sync_on_extraction": true, "replace_existing": false, "sync_mode": "incremental"}, "google_ads_sync": {"api_key": "**********************************************************************************", "base_id": "apptyeo8OtplQ8wAN", "table_id": "tbl6IbJmv7eqvOT2R", "auto_sync": false, "sync_mode": "incremental"}, "ghl_sync": {"api_key": "**********************************************************************************", "base_id": "app7ffftdM6e3yekG", "table_id": "tbliGenius Phone ", "auto_sync": false, "sync_mode": "smart_incremental"}}