2025-07-14 18:59:32 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-14 18:59:32 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Ready Set Repair
2025-07-14 18:59:32 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-14 18:59:32 | ERROR    | root | [ERROR] CLAUDE_API_KEY environment variable is required
2025-07-14 18:59:32 | ERROR    | root | [ERROR] AIRTABLE_API_KEY environment variable is required
2025-07-14 18:59:32 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-14 18:59:32 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-14 18:59:32 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-14 18:59:32 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-14 18:59:57 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-14 18:59:57 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Ready Set Repair
2025-07-14 18:59:57 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-14 18:59:57 | ERROR    | root | [ERROR] CLAUDE_API_KEY environment variable is required
2025-07-14 18:59:57 | ERROR    | root | [ERROR] AIRTABLE_API_KEY environment variable is required
2025-07-14 18:59:57 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-14 18:59:57 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-14 18:59:57 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-14 18:59:57 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-14 19:00:22 | INFO     | root | [STARTUP] Analytics Dashboard Server starting up...
2025-07-14 19:00:22 | INFO     | root | [INFO] Working directory: C:\Users\<USER>\Downloads\RL Tools\Ready Set Repair
2025-07-14 19:00:22 | INFO     | root | [INFO] Python version: 3.12.4 (tags/v3.12.4:8e8a4ba, Jun  6 2024, 19:30:16) [MSC v.1940 64 bit (AMD64)]
2025-07-14 19:00:22 | INFO     | root | [STARTUP] Starting server on 0.0.0.0:8000
2025-07-14 19:00:22 | INFO     | root | [ENV] Environment: PRODUCTION
2025-07-14 19:00:22 | INFO     | werkzeug | [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8000
 * Running on http://***********:8000
2025-07-14 19:00:22 | INFO     | werkzeug | [33mPress CTRL+C to quit[0m
2025-07-14 21:40:54 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:54] "GET / HTTP/1.1" 200 -
2025-07-14 21:40:55 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:55] "GET /styles.css HTTP/1.1" 200 -
2025-07-14 21:40:55 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:55] "GET /img/rl.svg HTTP/1.1" 200 -
2025-07-14 21:40:55 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:55] "GET /script.js HTTP/1.1" 200 -
2025-07-14 21:40:55 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:55] "GET /api/client-config HTTP/1.1" 200 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblA6ABFBTURfyZx9 HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblRBXdh6L6zm9CZn HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblcdFVUC3zJrbmNf HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[31m[1mGET /api/airtable/records?baseId=app7ffftdM6e3yekG&tableId=tblHyyZHUsTdEb3BL HTTP/1.1[0m" 403 -
2025-07-14 21:40:56 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:40:56] "[33mGET /favicon.ico HTTP/1.1[0m" 404 -
2025-07-14 21:41:06 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:41:06] "GET /api/latest-data-date HTTP/1.1" 200 -
2025-07-14 21:41:17 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:41:17] "[36mGET /styles.css HTTP/1.1[0m" 304 -
2025-07-14 21:41:17 | INFO     | werkzeug | 127.0.0.1 - - [14/Jul/2025 21:41:17] "[33mGET /.well-known/appspecific/com.chrome.devtools.json HTTP/1.1[0m" 404 -
